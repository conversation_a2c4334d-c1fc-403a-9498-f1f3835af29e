package constants

const (
	NOTIFICATION_SOURCE_CODE_ASSET_ASSIGNMENT        string = "ASSET_ASSIGNMENT"
	NOTIFICATION_SOURCE_CODE_TICKET_ASSIGNMENT       string = "TICKET_ASSIGNMENT"
	NOTIFICATION_SOURCE_CODE_TICKET_UPDATE           string = "TICKET_UPDATE"
	NOTIFICATION_SOURCE_CODE_IDENTITY_AUTHENTICATION string = "IDENTITY_AUTH"
	NOTIFICATION_SOURCE_CODE_WORK_ORDER              string = "WORK_ORDER"
	NOTIFICATION_SOURCE_CODE_TASK                    string = "TASK"
	NOTIFICATION_SOURCE_CODE_INSPECTION              string = "INSPECTION"
	NOTIFICATION_SOURCE_CODE_ASSET_VEHICLE           string = "ASSET_VEHICLE"
	NOTIFICATION_SOURCE_CODE_ASSET_TYRE              string = "ASSET_TYRE"
	NOTIFICATION_SOURCE_CODE_LINKED_TYRE             string = "LINKED_TYRE"
	NOTIFICATION_SOURCE_CODE_USER_MANAGEMENT         string = "USER_MANAGEMENT"
	NOTIFICATION_SOURCE_CODE_APPROVAL                string = "APPROVAL"
	NOTIFICATION_SOURCE_CODE_ALERT                   string = "ALERT"
	NOTIFICATION_SOURCE_CODE_ASSET_COMPONENT         string = "ASSET_COMPONENT"
	NOTIFICATION_SOURCE_CODE_ASSET_TRANSACTION       string = "ASSET_TRANSACTION"
	NOTIFICATION_SOURCE_CODE_ASSET                   string = "ASSET"
	NOTIFICATION_SOURCE_CODE_ASSET_HANDOVER          string = "ASSET_HANDOVER"
	NOTIFICATION_SOURCE_CODE_TYRE_ALERT              string = "TYRE_ALERT"
)

const (
	NOTIFICATION_TYPE_USER_ACTIVITY_CODE string = "USER_ACTIVITY"
	NOTIFICATION_TYPE_ALERT_CODE         string = "ALERT"
	NOTIFICATION_TYPE_REMINDER_CODE      string = "REMINDER"
)

const (
	DESTINATION_TYPE_ASSET                   string = "ASSET"
	DESTINATION_TYPE_ASSET_ALERT             string = "ASSET_ALERT"
	DESTINATION_TYPE_ASSET_ALERT_TYRE        string = "ASSET_ALERT_TYRE"
	DESTINATION_TYPE_WORK_ORDER              string = "WORK_ORDER"
	DESTINATION_TYPE_TASK                    string = "TASK"
	DESTINATION_TYPE_INSPECTION              string = "INSPECTION"
	DESTINATION_TYPE_TRACKING_SYTEM_PLAYBACK string = "TRACKING_SYSTEM_PLAYBACK"
	DESTINATION_TYPE_ASSET_HANDOVER          string = "ASSET_HANDOVER"
	DESTINATION_TYPE_APPROVAL                string = "APPROVAL"
)

const (
	NOTIF_COUNT_PARAMETER_REMINDER_OVERDUE string = "REMINDER_OVERDUE"
)

const (
	NOTIF_REF_ASSET          string = "ASSET"
	NOTIF_REF_WORK_ORDER     string = "WORK_ORDER"
	NOTIF_REF_TASK           string = "TASK"
	NOTIF_REF_INSPECTION     string = "INSPECTION"
	NOTIF_REF_APPROVAL       string = "APPROVAL"
	NOTIF_REF_ASSET_ALERT    string = "ASSET_ALERT"
	NOTIF_REF_ASSET_HANDOVER string = "ASSET_HANDOVER"
)

const (
	NOTIF_CONTENT_TYPE_CODE_ALERT   string = "ALERT"
	NOTIF_CONTENT_TYPE_CODE_WARNING string = "WARNING"
)
