package usecase

import (
	assetModel "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/geo/models"
	integrationConstants "assetfindr/internal/app/integration/constants"
	integrationModel "assetfindr/internal/app/integration/models"
	notificationConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	userModels "assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"html/template"
	"strconv"

	"context"

	"github.com/jackc/pgtype"
)

func (uc *TrackingUseCase) MonitorTyreAlertV2(ctx context.Context, tyreSensorData *models.TyreSensor, assetVehicle *assetModel.AssetVehicle, axleConfigs []assetModel.AxleConfiguration) {
	if !tyreSensorData.ParentAssetID.Valid {
		return
	}

	tyreAlertConfig, err := uc.tyreAlertRepo.GetTyreAlertConfig(ctx, uc.DB.DB(), integrationModel.TyreAlertConfigCondition{
		Where: integrationModel.TyreAlertConfigWhere{
			ParentAssetID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Warnf("failed to get tyre alert config, err: %v", err)
			return
		}
		return
	}

	uc.MonitorTyreAlertPressure(ctx, tyreSensorData, tyreAlertConfig, assetVehicle, axleConfigs)
	uc.MonitorTyreAlertHighTemperature(ctx, tyreSensorData, tyreAlertConfig)
}

const (
	labelPressure       = "pressure"
	labelPressureMax    = "pressure_max"
	labelPressureMin    = "pressure_min"
	labelTemperature    = "temperature"
	labelTemperatureMax = "temperature_max"
)

func (uc *TrackingUseCase) MonitorTyreAlertPressure(ctx context.Context, tyreSensorData *models.TyreSensor, tyreAlertConfig *integrationModel.TyreAlertConfig, assetVehicle *assetModel.AssetVehicle, axleConfigs []assetModel.AxleConfiguration) {
	if !tyreAlertConfig.UsePressureAlert {
		return
	}

	if !tyreSensorData.Pressure.Valid {
		return
	}

	mapPositionToPressureFromAxle := assetModel.MapPositionToPressureFromAxle(axleConfigs)
	pressureConfig := mapPositionToPressureFromAxle[int(tyreSensorData.TyrePosition.Int64)]
	if !pressureConfig.PressureMin.Valid && !pressureConfig.PressureMax.Valid {
		return
	}

	recordedValue := pgtype.JSONB{}
	recordedValue.Set(map[string]interface{}{
		labelPressure: tyreSensorData.Pressure,
	})

	if pressureConfig.PressureMax.Valid && tyreSensorData.Pressure.Float64 > float64(pressureConfig.PressureMax.Int64) {
		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED)
		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelPressureMax: pressureConfig.PressureMax,
		})
		tyreAlert := &integrationModel.TyreAlert{
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err := uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}

		// Send notification if configured
		if tyreAlertConfig.UseSendNotification {
			go uc.sendTyreAlertNotification(ctx, tyreSensorData, tyreAlertConfig, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED,
				strconv.FormatFloat(tyreSensorData.Pressure.Float64, 'f', 2, 64),
				strconv.FormatInt(pressureConfig.PressureMax.Int64, 10), "", "", "")
		}
	}

	if pressureConfig.PressureMin.Valid && tyreSensorData.Pressure.Float64 < float64(pressureConfig.PressureMin.Int64) {
		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED)
		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelPressureMin: pressureConfig.PressureMin,
		})
		tyreAlert := &integrationModel.TyreAlert{
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err := uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}

		// Send notification if configured
		if tyreAlertConfig.UseSendNotification {
			go uc.sendTyreAlertNotification(ctx, tyreSensorData, tyreAlertConfig, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED,
				strconv.FormatFloat(tyreSensorData.Pressure.Float64, 'f', 2, 64),
				"", strconv.FormatInt(pressureConfig.PressureMin.Int64, 10), "", "")
		}
	}
}

func (uc *TrackingUseCase) MonitorTyreAlertHighTemperature(ctx context.Context, tyreSensorData *models.TyreSensor, tyreAlertConfig *integrationModel.TyreAlertConfig) {
	if !tyreAlertConfig.UseHighTemperatureAlert {
		return
	}

	if !tyreSensorData.Temperature.Valid {
		return
	}

	if !tyreAlertConfig.MaxTemperatureThreshold.Valid {
		return
	}

	if tyreSensorData.Temperature.Float64 > tyreAlertConfig.MaxTemperatureThreshold.Float64 {
		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE)
		recordedValue := pgtype.JSONB{}
		recordedValue.Set(map[string]interface{}{
			labelTemperature: tyreSensorData.Temperature,
		})

		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelTemperatureMax: tyreAlertConfig.MaxTemperatureThreshold,
		})
		tyreAlert := &integrationModel.TyreAlert{
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err := uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}

		// Send notification if configured
		if tyreAlertConfig.UseSendNotification {
			go uc.sendTyreAlertNotification(ctx, tyreSensorData, tyreAlertConfig, integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE,
				"", "", "",
				strconv.FormatFloat(tyreSensorData.Temperature.Float64, 'f', 2, 64),
				strconv.FormatFloat(tyreAlertConfig.MaxTemperatureThreshold.Float64, 'f', 2, 64))
		}
	}
}

// sendTyreAlertNotification sends notifications for tyre alerts
func (uc *TrackingUseCase) sendTyreAlertNotification(
	ctx context.Context,
	tyreSensorData *models.TyreSensor,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	alertType string,
	currentPressure string,
	thresholdMaxPressure string,
	thresholdMinPressure string,
	currentTemperature string,
	thresholdTemperature string,
) {
	// Get asset information
	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.AssetID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get asset for tyre alert notification, err: %v", err)
		return
	}

	// Get parent asset (vehicle) information
	parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get parent asset for tyre alert notification, err: %v", err)
		return
	}

	// Get client information
	client, err := uc.userRepo.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get client for tyre alert notification, err: %v", err)
		return
	}

	// Generate target URL
	targetURL := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstants.DESTINATION_TYPE_ASSET_ALERT_TYRE, tyreSensorData.ParentAssetID.String)

	// Create template data
	tyrePosition := ""
	if tyreSensorData.TyrePosition.Valid {
		tyrePosition = strconv.FormatInt(tyreSensorData.TyrePosition.Int64, 10)
	}

	templ := tmplhelpers.TyreAlertTemplate{
		AlertType:            alertType,
		TyreSerialNumber:     asset.SerialNumber,
		TyrePosition:         tyrePosition,
		AssetIDCredential:    parentAsset.GetAssetIdentForNotif(),
		CurrentPressure:      currentPressure,
		ThresholdMaxPressure: thresholdMaxPressure,
		ThresholdMinPressure: thresholdMinPressure,
		CurrentTemperature:   currentTemperature,
		ThresholdTemperature: thresholdTemperature,
		RedirectLink:         template.URL(targetURL),
	}

	// Determine notification recipients
	userIDs := make([]string, 0)

	// Add configured recipient users
	for _, userID := range tyreAlertConfig.NotificationRecipientUserIDs {
		userIDs = append(userIDs, userID)
	}

	// Add asset assignee if configured
	if tyreAlertConfig.NotifyAssetAssignee {
		assetAssignment, err := uc.assetAssignmentRepo.GetAssetAssignment(ctx, uc.DB.DB(), assetModel.AssetAssignmentCondition{
			Where: assetModel.AssetAssignmentWhere{
				AssetID: tyreSensorData.ParentAssetID.String,
			},
		})
		if err == nil && assetAssignment.UserID != "" {
			userIDs = append(userIDs, assetAssignment.UserID)
		}
	}

	if len(userIDs) == 0 {
		commonlogger.Warnf("no recipients configured for tyre alert notification")
		return
	}

	// Create notification items
	notifItems := make([]notificationDtos.CreateNotificationItem, 0, len(userIDs))
	for _, userID := range userIDs {
		notifItem := notificationDtos.CreateNotificationItem{
			UserID:            userID,
			SourceCode:        notificationConstants.NOTIFICATION_SOURCE_CODE_TYRE_ALERT,
			SourceReferenceID: tyreSensorData.AssetID,
			TargetReferenceID: tyreSensorData.ParentAssetID.String,
			TargetURL:         targetURL,
			MessageHeader:     templ.GenerateEmailSubject(),
			MessageBody:       templ.GenerateEmailBody(),
			MessageFirebase: notificationDtos.MessageFirebase{
				Title: templ.GeneratePushNotifSubject(),
				Body:  templ.GeneratePushNotifBody(),
			},
			ClientID:        asset.ClientID,
			TypeCode:        notificationConstants.NOTIFICATION_TYPE_ALERT_CODE,
			ContentTypeCode: notificationConstants.NOTIF_CONTENT_TYPE_CODE_ALERT,
			ReferenceCode:   notificationConstants.NOTIF_REF_ASSET_ALERT,
			ReferenceValue:  tyreSensorData.AssetID,
		}
		notifItems = append(notifItems, notifItem)
	}

	// Determine notification methods based on action types
	sendToEmail := false
	sendToPushNotif := false
	for _, actionType := range tyreAlertConfig.NotificationActionTypes {
		switch actionType {
		case integrationConstants.ALERT_ACTION_TYPE_CODE_EMAIL_NOTIFICATION:
			sendToEmail = true
		case integrationConstants.ALERT_ACTION_TYPE_CODE_WEB_NOTIFICATION, integrationConstants.ALERT_ACTION_TYPE_CODE_MOBILE_APP_NOTIFICATION, integrationConstants.ALERT_ACTION_TYPE_CODE_WEB_MOBILE_APP_NOTIFICATION:
			sendToPushNotif = true
		}
	}

	// Send notifications
	createNotifReq := notificationDtos.CreateNotificationReq{
		Items:           notifItems,
		SendToEmail:     sendToEmail,
		SendToPushNotif: sendToPushNotif,
	}

	err = uc.notifUseCase.CreateNotification(ctx, createNotifReq)
	if err != nil {
		commonlogger.Errorf("failed to send tyre alert notification, err: %v", err)
	}
}
