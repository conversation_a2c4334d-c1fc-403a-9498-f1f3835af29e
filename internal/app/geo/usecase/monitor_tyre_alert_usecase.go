package usecase

import (
	assetModel "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/geo/models"
	integrationConstants "assetfindr/internal/app/integration/constants"
	integrationModel "assetfindr/internal/app/integration/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"

	"context"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

func (uc *TrackingUseCase) MonitorTyreAlertV2(ctx context.Context, tyreSensorData *models.TyreSensor, assetVehicle *assetModel.AssetVehicle, axleConfigs []assetModel.AxleConfiguration) {
	if !tyreSensorData.ParentAssetID.Valid {
		return
	}

	tyreAlertConfig, err := uc.tyreAlertRepo.GetTyreAlertConfig(ctx, uc.DB.DB(), integrationModel.TyreAlertConfigCondition{
		Where: integrationModel.TyreAlertConfigWhere{
			ParentAssetID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Warnf("failed to get tyre alert config, err: %v", err)
			return
		}
		return
	}

	uc.MonitorTyreAlertPressure(ctx, tyreSensorData, tyreAlertConfig, assetVehicle, axleConfigs)
	uc.MonitorTyreAlertHighTemperature(ctx, tyreSensorData, tyreAlertConfig)
}

func generateRecordedValuePressure(pressure null.Float) pgtype.JSONB {
	result := pgtype.JSONB{}
	result.Set(map[string]interface{}{
		"pressure": pressure,
	})
	return result
}

const (
	labelPressure       = "pressure"
	labelPressureMax    = "pressure_max"
	labelPressureMin    = "pressure_min"
	labelTemperature    = "temperature"
	labelTemperatureMax = "temperature_max"
)

func (uc *TrackingUseCase) MonitorTyreAlertPressure(ctx context.Context, tyreSensorData *models.TyreSensor, tyreAlertConfig *integrationModel.TyreAlertConfig, assetVehicle *assetModel.AssetVehicle, axleConfigs []assetModel.AxleConfiguration) {
	if !tyreAlertConfig.UsePressureAlert {
		return
	}

	if !tyreSensorData.Pressure.Valid {
		return
	}

	mapPositionToPressureFromAxle := assetModel.MapPositionToPressureFromAxle(axleConfigs)
	pressureConfig := mapPositionToPressureFromAxle[int(tyreSensorData.TyrePosition.Int64)]
	if !pressureConfig.PressureMin.Valid && !pressureConfig.PressureMax.Valid {
		return
	}

	recordedValue := pgtype.JSONB{}
	recordedValue.Set(map[string]interface{}{
		labelPressure: tyreSensorData.Pressure,
	})

	if pressureConfig.PressureMax.Valid && tyreSensorData.Pressure.Float64 > float64(pressureConfig.PressureMax.Int64) {
		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED)
		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelPressureMax: pressureConfig.PressureMax,
		})
		tyreAlert := &integrationModel.TyreAlert{
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err := uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}
	}

	if pressureConfig.PressureMin.Valid && tyreSensorData.Pressure.Float64 < float64(pressureConfig.PressureMin.Int64) {
		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED)
		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelPressureMin: pressureConfig.PressureMin,
		})
		tyreAlert := &integrationModel.TyreAlert{
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err := uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}
	}
}

func (uc *TrackingUseCase) MonitorTyreAlertHighTemperature(ctx context.Context, tyreSensorData *models.TyreSensor, tyreAlertConfig *integrationModel.TyreAlertConfig) {
	if !tyreAlertConfig.UseHighTemperatureAlert {
		return
	}

	if !tyreSensorData.Temperature.Valid {
		return
	}

	if !tyreAlertConfig.MaxTemperatureThreshold.Valid {
		return
	}

	if tyreSensorData.Temperature.Float64 > tyreAlertConfig.MaxTemperatureThreshold.Float64 {
		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE)
		recordedValue := pgtype.JSONB{}
		recordedValue.Set(map[string]interface{}{
			labelTemperature: tyreSensorData.Temperature,
		})

		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelTemperatureMax: tyreAlertConfig.MaxTemperatureThreshold,
		})
		tyreAlert := &integrationModel.TyreAlert{
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err := uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}
	}
}
