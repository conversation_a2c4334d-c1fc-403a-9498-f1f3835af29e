package tmplhelpers

import (
	"html/template"
)

type TyreAlertTemplate struct {
	AlertType           string // PRESSURE_OVERINFLATED, PRESSURE_UNDERINFLATED, HIGH_TEMPERATURE
	TyreSerialNumber    string
	TyrePosition        string
	AssetIDCredential   string
	CurrentPressure     string
	ThresholdMaxPressure string
	ThresholdMinPressure string
	CurrentTemperature  string
	ThresholdTemperature string
	RedirectLink        template.URL
}

// GenerateEmailSubject creates the email subject based on alert type
func (t TyreAlertTemplate) GenerateEmailSubject() string {
	switch t.AlertType {
	case "PRESSURE_OVERINFLATED":
		title, _ := ParseStringTemplate("Tyre Overinflated Alert - {{.AssetIDCredential}}", t)
		return title
	case "PRESSURE_UNDERINFLATED":
		title, _ := ParseStringTemplate("Tyre Underinflated Alert - {{.AssetIDCredential}}", t)
		return title
	case "HIGH_TEMPERATURE":
		title, _ := ParseStringTemplate("Tyre High Temperature Alert - {{.AssetIDCredential}}", t)
		return title
	default:
		return "Tyre Alert"
	}
}

// GenerateEmailBody creates the email body content based on alert type
func (t TyreAlertTemplate) GenerateEmailBody() string {
	switch t.AlertType {
	case "PRESSURE_OVERINFLATED":
		body, _ := ParseStringTemplate(`Tyre {{.TyreSerialNumber}} on your vehicle {{.AssetIDCredential}} ((Tyre #{{.TyrePosition}})) is experiencing overinflated.

Current Pressure: {{.CurrentPressure}} psi
Maximum Allowed Pressure: {{.ThresholdMaxPressure}} psi
Please check your tyre pressure as soon as possible to prevent potential issues.

<br><br>
<a href="{{.RedirectLink}}">
<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
View Asset
</button>
</a>
<br>`, t)
		return body
	case "PRESSURE_UNDERINFLATED":
		body, _ := ParseStringTemplate(`Tyre {{.TyreSerialNumber}} on your vehicle {{.AssetIDCredential}} ((Tyre #{{.TyrePosition}})) is experiencing underinflated.

Current Pressure: {{.CurrentPressure}} psi
Minimum Allowed Pressure: {{.ThresholdMinPressure}} psi
Please check your tyre pressure as soon as possible to prevent potential issues.

<br><br>
<a href="{{.RedirectLink}}">
<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
View Asset
</button>
</a>
<br>`, t)
		return body
	case "HIGH_TEMPERATURE":
		body, _ := ParseStringTemplate(`Tyre {{.TyreSerialNumber}} on your vehicle {{.AssetIDCredential}} ((Tyre #{{.TyrePosition}})) has reached a high temperature.

Current Temperature: {{.CurrentTemperature}} °C
Temperature Threshold: {{.ThresholdTemperature}} °C

High tyre temperature can indicate a problem, please investigate immediately.

<br><br>
<a href="{{.RedirectLink}}">
<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
View Asset
</button>
</a>
<br>`, t)
		return body
	default:
		return "Tyre alert detected."
	}
}

// GeneratePushNotifSubject creates the push notification subject based on alert type
func (t TyreAlertTemplate) GeneratePushNotifSubject() string {
	switch t.AlertType {
	case "PRESSURE_OVERINFLATED":
		return "Tyre Overinflated Alert"
	case "PRESSURE_UNDERINFLATED":
		return "Tyre Underinflated Alert"
	case "HIGH_TEMPERATURE":
		return "Tyre High Temperature Alert"
	default:
		return "Tyre Alert"
	}
}

// GeneratePushNotifBody creates the push notification body content based on alert type
func (t TyreAlertTemplate) GeneratePushNotifBody() string {
	switch t.AlertType {
	case "PRESSURE_OVERINFLATED":
		body, _ := ParseStringTemplate("Tyre {{.TyrePosition}} is detected with overinflated. Current pressure: {{.CurrentPressure}} psi, Threshold Max: {{.ThresholdMaxPressure}} psi.", t)
		return body
	case "PRESSURE_UNDERINFLATED":
		body, _ := ParseStringTemplate("Tyre {{.TyrePosition}} is detected with underinflated. Current pressure: {{.CurrentPressure}} psi, Threshold Min: {{.ThresholdMinPressure}} psi.", t)
		return body
	case "HIGH_TEMPERATURE":
		body, _ := ParseStringTemplate("Tyre {{.TyreSerialNumber}} ( {{.AssetIDCredential}} -  Tyre #{{.TyrePosition}}) temperature is high. Current temperature: {{.CurrentTemperature}}°C, Threshold: {{.ThresholdTemperature}}°C", t)
		return body
	default:
		return "Tyre alert detected."
	}
}
