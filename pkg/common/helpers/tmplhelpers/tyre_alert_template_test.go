package tmplhelpers

import (
	"html/template"
	"strings"
	"testing"
)

func TestTyreAlertTemplate_GenerateEmailSubject(t *testing.T) {
	tests := []struct {
		name      string
		alertType string
		assetID   string
		expected  string
	}{
		{
			name:      "Overinflated alert",
			alertType: "PRESSURE_OVERINFLATED",
			assetID:   "VEH-001",
			expected:  "Tyre Overinflated Alert - VEH-001",
		},
		{
			name:      "Underinflated alert",
			alertType: "PRESSURE_UNDERINFLATED",
			assetID:   "VEH-002",
			expected:  "Tyre Underinflated Alert - VEH-002",
		},
		{
			name:      "High temperature alert",
			alertType: "HIGH_TEMPERATURE",
			assetID:   "VEH-003",
			expected:  "Tyre High Temperature Alert - VEH-003",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			templ := TyreAlertTemplate{
				AlertType:         tt.alertType,
				AssetIDCredential: tt.assetID,
			}
			result := templ.GenerateEmailSubject()
			if result != tt.expected {
				t.<PERSON><PERSON><PERSON>("GenerateEmailSubject() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestTyreAlertTemplate_GeneratePushNotifSubject(t *testing.T) {
	tests := []struct {
		name      string
		alertType string
		expected  string
	}{
		{
			name:      "Overinflated alert",
			alertType: "PRESSURE_OVERINFLATED",
			expected:  "Tyre Overinflated Alert",
		},
		{
			name:      "Underinflated alert",
			alertType: "PRESSURE_UNDERINFLATED",
			expected:  "Tyre Underinflated Alert",
		},
		{
			name:      "High temperature alert",
			alertType: "HIGH_TEMPERATURE",
			expected:  "Tyre High Temperature Alert",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			templ := TyreAlertTemplate{
				AlertType: tt.alertType,
			}
			result := templ.GeneratePushNotifSubject()
			if result != tt.expected {
				t.Errorf("GeneratePushNotifSubject() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestTyreAlertTemplate_GeneratePushNotifBody(t *testing.T) {
	tests := []struct {
		name             string
		alertType        string
		tyrePosition     string
		currentPressure  string
		thresholdMax     string
		thresholdMin     string
		tyreSerial       string
		assetID          string
		currentTemp      string
		thresholdTemp    string
		expectedContains []string
	}{
		{
			name:             "Overinflated alert",
			alertType:        "PRESSURE_OVERINFLATED",
			tyrePosition:     "1",
			currentPressure:  "45.5",
			thresholdMax:     "40.0",
			expectedContains: []string{"Tyre 1", "overinflated", "45.5 psi", "40.0 psi"},
		},
		{
			name:             "Underinflated alert",
			alertType:        "PRESSURE_UNDERINFLATED",
			tyrePosition:     "2",
			currentPressure:  "25.0",
			thresholdMin:     "30.0",
			expectedContains: []string{"Tyre 2", "underinflated", "25.0 psi", "30.0 psi"},
		},
		{
			name:             "High temperature alert",
			alertType:        "HIGH_TEMPERATURE",
			tyreSerial:       "TYR-123",
			assetID:          "VEH-001",
			tyrePosition:     "3",
			currentTemp:      "85.5",
			thresholdTemp:    "80.0",
			expectedContains: []string{"TYR-123", "VEH-001", "Tyre #3", "85.5°C", "80.0°C"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			templ := TyreAlertTemplate{
				AlertType:            tt.alertType,
				TyreSerialNumber:     tt.tyreSerial,
				TyrePosition:         tt.tyrePosition,
				AssetIDCredential:    tt.assetID,
				CurrentPressure:      tt.currentPressure,
				ThresholdMaxPressure: tt.thresholdMax,
				ThresholdMinPressure: tt.thresholdMin,
				CurrentTemperature:   tt.currentTemp,
				ThresholdTemperature: tt.thresholdTemp,
			}
			result := templ.GeneratePushNotifBody()
			
			for _, expected := range tt.expectedContains {
				if !strings.Contains(result, expected) {
					t.Errorf("GeneratePushNotifBody() = %v, should contain %v", result, expected)
				}
			}
		})
	}
}

func TestTyreAlertTemplate_GenerateEmailBody(t *testing.T) {
	templ := TyreAlertTemplate{
		AlertType:            "PRESSURE_OVERINFLATED",
		TyreSerialNumber:     "TYR-123",
		TyrePosition:         "1",
		AssetIDCredential:    "VEH-001",
		CurrentPressure:      "45.5",
		ThresholdMaxPressure: "40.0",
		RedirectLink:         template.URL("https://example.com/asset/123"),
	}

	result := templ.GenerateEmailBody()
	
	expectedContains := []string{
		"TYR-123",
		"VEH-001",
		"Tyre #1",
		"overinflated",
		"45.5 psi",
		"40.0 psi",
		"View Asset",
		"https://example.com/asset/123",
	}

	for _, expected := range expectedContains {
		if !strings.Contains(result, expected) {
			t.Errorf("GenerateEmailBody() should contain %v, got: %v", expected, result)
		}
	}
}
